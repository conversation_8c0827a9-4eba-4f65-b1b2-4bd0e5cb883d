{%- comment -%}
Add Facebook and Pinterest Open Graph meta tags to all pages
for friendly Facebook sharing and Pinterest pinning.

More info Open Graph meta tags
- https://developers.facebook.com/docs/opengraph/using-objects/
- https://developers.pinterest.com/rich_pins/

Use the Facebook Open Graph Debugger for validation (and cache clearing)
- https://developers.facebook.com/tools/debug

Validate your Pinterest rich pins
- https://developers.pinterest.com/tools/url-debugger/
{%- endcomment -%}

{%- comment -%}Required Open Graph meta tags{%- endcomment -%}
<meta property="og:site_name" content="{{ shop.name | escape }}">
<meta property="og:url" content="{{ shop.url }}{{ canonical_url }}">

{%- comment -%}Set og:type based on page type{%- endcomment -%}
{%- if request.page_type == 'product' -%}
  <meta property="og:type" content="product">
  <meta property="og:title" content="{{ product.title | strip_html | escape }}">
  {%- if product.description != blank -%}
    <meta property="og:description" content="{{ product.description | strip_html | truncatewords: 25, '' | escape }}">
  {%- elsif page_description -%}
    <meta property="og:description" content="{{ page_description | strip_html | escape }}">
  {%- else -%}
    <meta property="og:description" content="{{ shop.name | escape }} - {{ product.title | strip_html | escape }}">
  {%- endif -%}
  <meta property="product:price:amount" content="{{ product.selected_or_first_available_variant.price | money_without_currency | strip_html | escape }}">
  <meta property="product:price:currency" content="{{ cart.currency.iso_code }}">
{%- elsif request.page_type == 'article' -%}
  <meta property="og:type" content="article">
  <meta property="og:title" content="{{ article.title | strip_html | escape }}">
  {%- if article.excerpt != blank -%}
    <meta property="og:description" content="{{ article.excerpt | strip_html | escape }}">
  {%- elsif article.content != blank -%}
    <meta property="og:description" content="{{ article.content | strip_html | truncatewords: 25, '' | escape }}">
  {%- elsif page_description -%}
    <meta property="og:description" content="{{ page_description | strip_html | escape }}">
  {%- else -%}
    <meta property="og:description" content="{{ shop.name | escape }} - {{ article.title | strip_html | escape }}">
  {%- endif -%}
{%- elsif request.page_type == 'collection' -%}
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ collection.title | strip_html | escape }}">
  {%- if collection.description != blank -%}
    <meta property="og:description" content="{{ collection.description | strip_html | escape }}">
  {%- elsif page_description -%}
    <meta property="og:description" content="{{ page_description | strip_html | escape }}">
  {%- else -%}
    <meta property="og:description" content="{{ shop.name | escape }} - {{ collection.title | strip_html | escape }}">
  {%- endif -%}
{%- elsif template == 'index' -%}
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ page_title | default: shop.name | escape }}">
  {%- if page_description -%}
    <meta property="og:description" content="{{ page_description | strip_html | escape }}">
  {%- elsif shop.description != blank -%}
    <meta property="og:description" content="{{ shop.description | strip_html | escape }}">
  {%- else -%}
    <meta property="og:description" content="{{ shop.name | escape }}">
  {%- endif -%}
{%- else -%}
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ page_title | default: shop.name | escape }}">
  {%- if page_description -%}
    <meta property="og:description" content="{{ page_description | strip_html | escape }}">
  {%- elsif shop.description != blank -%}
    <meta property="og:description" content="{{ shop.description | strip_html | escape }}">
  {%- else -%}
    <meta property="og:description" content="{{ shop.name | escape }}">
  {%- endif -%}
{%- endif -%}

{%- comment -%}Open Graph image with proper fallbacks{%- endcomment -%}
{%- if page_image -%}
  <meta property="og:image" content="https:{{ page_image | image_url: width: 1200, height: 630 }}">
  <meta property="og:image:secure_url" content="https:{{ page_image | image_url: width: 1200, height: 630 }}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  {%- if page_image.alt -%}
    <meta property="og:image:alt" content="{{ page_image.alt | escape }}">
  {%- endif -%}
{%- elsif settings.logo -%}
  <meta property="og:image" content="https:{{ settings.logo | image_url: width: 1200, height: 630 }}">
  <meta property="og:image:secure_url" content="https:{{ settings.logo | image_url: width: 1200, height: 630 }}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  {%- if settings.logo.alt -%}
    <meta property="og:image:alt" content="{{ settings.logo.alt | escape }}">
  {%- endif -%}
{%- elsif shop.brand.logo -%}
  <meta property="og:image" content="https:{{ shop.brand.logo | image_url: width: 1200, height: 630 }}">
  <meta property="og:image:secure_url" content="https:{{ shop.brand.logo | image_url: width: 1200, height: 630 }}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
{%- endif -%}

{%- comment -%}
This snippet renders meta data needed to create a Twitter card
for products and articles.

Your cards must be approved by Twitter to be activated
- https://dev.twitter.com/docs/cards/validation/validator

More information:
- https://dev.twitter.com/cards/types/summary
{%- endcomment -%}

<meta name="twitter:card" content="summary_large_image">

{%- if request.page_type == 'product' -%}
  <meta name="twitter:title" content="{{ product.title | strip_html | escape }}">
  {%- if product.description != blank -%}
    <meta name="twitter:description" content="{{ product.description | strip_html | truncatewords: 25, '' | escape }}">
  {%- elsif page_description -%}
    <meta name="twitter:description" content="{{ page_description | strip_html | escape }}">
  {%- else -%}
    <meta name="twitter:description" content="{{ shop.name | escape }} - {{ product.title | strip_html | escape }}">
  {%- endif -%}
{%- elsif request.page_type == 'article' -%}
  <meta name="twitter:title" content="{{ article.title | strip_html | escape }}">
  {%- if article.excerpt != blank -%}
    <meta name="twitter:description" content="{{ article.excerpt | strip_html | escape }}">
  {%- elsif article.content != blank -%}
    <meta name="twitter:description" content="{{ article.content | strip_html | truncatewords: 25, '' | escape }}">
  {%- elsif page_description -%}
    <meta name="twitter:description" content="{{ page_description | strip_html | escape }}">
  {%- else -%}
    <meta name="twitter:description" content="{{ shop.name | escape }} - {{ article.title | strip_html | escape }}">
  {%- endif -%}
{%- elsif request.page_type == 'collection' -%}
  <meta name="twitter:title" content="{{ collection.title | strip_html | escape }}">
  {%- if collection.description != blank -%}
    <meta name="twitter:description" content="{{ collection.description | strip_html | escape }}">
  {%- elsif page_description -%}
    <meta name="twitter:description" content="{{ page_description | strip_html | escape }}">
  {%- else -%}
    <meta name="twitter:description" content="{{ shop.name | escape }} - {{ collection.title | strip_html | escape }}">
  {%- endif -%}
{%- elsif template == 'index' -%}
  <meta name="twitter:title" content="{{ page_title | default: shop.name | escape }}">
  {%- if page_description -%}
    <meta name="twitter:description" content="{{ page_description | strip_html | escape }}">
  {%- elsif shop.description != blank -%}
    <meta name="twitter:description" content="{{ shop.description | strip_html | escape }}">
  {%- else -%}
    <meta name="twitter:description" content="{{ shop.name | escape }}">
  {%- endif -%}
{%- else -%}
  <meta name="twitter:title" content="{{ page_title | default: shop.name | escape }}">
  {%- if page_description -%}
    <meta name="twitter:description" content="{{ page_description | strip_html | escape }}">
  {%- elsif shop.description != blank -%}
    <meta name="twitter:description" content="{{ shop.description | strip_html | escape }}">
  {%- else -%}
    <meta name="twitter:description" content="{{ shop.name | escape }}">
  {%- endif -%}
{%- endif -%}

{%- comment -%}Twitter card image with proper fallbacks{%- endcomment -%}
{%- if page_image -%}
  <meta name="twitter:image" content="https:{{ page_image | image_url: width: 1200, height: 630 }}">
  {%- if page_image.alt -%}
    <meta name="twitter:image:alt" content="{{ page_image.alt | escape }}">
  {%- endif -%}
{%- elsif settings.logo -%}
  <meta name="twitter:image" content="https:{{ settings.logo | image_url: width: 1200, height: 630 }}">
  {%- if settings.logo.alt -%}
    <meta name="twitter:image:alt" content="{{ settings.logo.alt | escape }}">
  {%- endif -%}
{%- elsif shop.brand.logo -%}
  <meta name="twitter:image" content="https:{{ shop.brand.logo | image_url: width: 1200, height: 630 }}">
{%- endif -%}